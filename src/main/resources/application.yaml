spring:
  application:
    name: tradej
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************
    username: root
    password: asswecan
  ai:
    openai:
      api-key: AIzaSyAcEWBGeoyr8m_D5_HPLmJOGOlLQtDFo-g
      base-url: https://generativelanguage.googleapis.com/v1beta/openai/
      chat:
        completions-path: /chat/completions

forest:
  max-connections: 1000        # 连接池最大连接数
  connect-timeout: 3000        # 连接超时时间，单位为毫秒
  read-timeout: 3000