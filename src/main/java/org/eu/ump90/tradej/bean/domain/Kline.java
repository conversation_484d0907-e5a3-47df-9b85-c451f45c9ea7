package org.eu.ump90.tradej.bean.domain;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class Kline {
  private Long id;

  private String symbol;

  private Long openTime;

  private BigDecimal open;

  private BigDecimal high;

  private BigDecimal low;

  private BigDecimal close;

  private BigDecimal volume;

  private Long closeTime;

  private BigDecimal quoteAssetVolume;

  private Integer numberOfTrades;

  private BigDecimal takerBuyBaseAssetVolume;

  private BigDecimal takerBuyQuoteAssetVolume;

  private String ignoreField;

  private String interval;
}
