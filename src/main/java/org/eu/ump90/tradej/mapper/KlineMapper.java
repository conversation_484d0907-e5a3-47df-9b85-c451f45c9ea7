package org.eu.ump90.tradej.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.eu.ump90.tradej.bean.domain.Kline;

@Mapper
public interface KlineMapper {
  int deleteByPrimaryKey(Long id);

  int insert(Kline record);

  int insertSelective(Kline record);

  Kline selectByPrimaryKey(Long id);

  int updateByPrimaryKeySelective(Kline record);

  int updateByPrimaryKey(Kline record);

  int batchInsert(List<Kline> records);

  List<Kline> findBySymbol(String symbol);
}
