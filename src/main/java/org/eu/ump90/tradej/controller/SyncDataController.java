package org.eu.ump90.tradej.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eu.ump90.tradej.service.SyncDataService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "sync")
@RequiredArgsConstructor
@Slf4j
public class SyncDataController {

  private final SyncDataService syncDataService;

  @GetMapping
  public String syncDate(
      @RequestParam String symbol,
      @RequestParam String interval,
      @RequestParam String startTime,
      @RequestParam String endTime) {

    try {
      log.info(
          "收到同步数据请求: symbol={}, interval={}, startTime={}, endTime={}",
          symbol,
          interval,
          startTime,
          endTime);

      // 调用服务层进行数据同步
      syncDataService.syncData(symbol, interval, startTime, endTime);

      return "数据同步成功";
    } catch (Exception e) {
      log.error("数据同步失败: {}", e.getMessage(), e);
      return "数据同步失败: " + e.getMessage();
    }
  }
}
