# Spring Boot 日志配置说明

## 概述

本项目已配置完整的日志系统，使用Logback作为日志框架，支持多环境配置和异步日志输出。

## 配置文件说明

### 1. 主要配置文件

- `logback-spring.xml` - Logback主配置文件，定义了日志输出格式、文件滚动策略等
- `application.yaml` - Spring Boot主配置文件，包含基础日志配置
- `application-dev.yaml` - 开发环境日志配置
- `application-test.yaml` - 测试环境日志配置  
- `application-prod.yaml` - 生产环境日志配置

### 2. 日志输出位置

- **控制台输出**: 开发和测试环境会输出到控制台
- **文件输出**: 所有环境都会输出到文件
  - 普通日志: `./logs/tradej.log`
  - 错误日志: `./logs/error.log`
  - 按天滚动: `./logs/tradej.2024-01-01.log`

### 3. 日志级别配置

#### 开发环境 (dev)
- 项目包 (`org.eu.ump`): DEBUG
- MyBatis: DEBUG (显示SQL语句)
- Forest HTTP客户端: DEBUG
- Spring框架: INFO

#### 测试环境 (test)  
- 项目包 (`org.eu.ump`): INFO
- MyBatis: INFO
- Forest HTTP客户端: INFO
- Spring框架: WARN

#### 生产环境 (prod)
- 项目包 (`org.eu.ump`): INFO
- MyBatis: WARN (不显示SQL)
- Forest HTTP客户端: WARN
- Spring框架: WARN

## 使用方法

### 1. 在代码中使用日志

使用Lombok的`@Slf4j`注解：

```java
@Slf4j
@RestController
public class YourController {
    
    public String someMethod() {
        log.info("这是一条信息日志");
        log.debug("这是一条调试日志: {}", someVariable);
        log.warn("这是一条警告日志");
        log.error("这是一条错误日志", exception);
        return "success";
    }
}
```

### 2. 切换环境

通过设置`spring.profiles.active`来切换环境：

```yaml
spring:
  profiles:
    active: dev  # 可选: dev, test, prod
```

或通过启动参数：
```bash
java -jar tradej.jar --spring.profiles.active=prod
```

### 3. 测试日志功能

启动应用后，访问以下接口测试日志功能：

- `GET /api/log/test` - 测试不同级别日志输出
- `GET /api/log/error` - 测试异常日志记录
- `GET /api/log/structured` - 测试结构化日志
- `GET /api/log/performance` - 测试性能日志

## 日志特性

### 1. 异步日志
- 使用异步Appender提高性能
- 队列大小: 512
- 不丢失日志策略

### 2. 日志滚动
- 按天滚动日志文件
- 单文件最大100MB
- 保留30天历史日志

### 3. 错误日志分离
- 错误日志单独输出到error.log
- 便于错误监控和告警

### 4. 彩色控制台输出
- 开发环境支持彩色日志输出
- 提高开发调试体验

## 日志格式

```
2024-01-01 12:00:00.123 [http-nio-8080-exec-1] INFO  o.e.u.t.controller.LogTestController - 用户请求日志测试接口
```

格式说明：
- `2024-01-01 12:00:00.123` - 时间戳
- `[http-nio-8080-exec-1]` - 线程名
- `INFO` - 日志级别
- `o.e.u.t.controller.LogTestController` - 类名(简化)
- `用户请求日志测试接口` - 日志消息

## 最佳实践

### 1. 日志级别使用建议
- **TRACE**: 最详细的调试信息，通常不使用
- **DEBUG**: 调试信息，开发环境使用
- **INFO**: 重要的业务流程信息
- **WARN**: 警告信息，需要关注但不影响运行
- **ERROR**: 错误信息，需要立即处理

### 2. 日志内容建议
- 使用参数化日志: `log.info("用户{}执行了{}", userId, action)`
- 记录关键业务节点
- 异常必须记录完整堆栈信息
- 避免在循环中大量输出日志

### 3. 性能考虑
- 生产环境关闭DEBUG日志
- 使用异步日志提高性能
- 合理设置日志保留策略

## 监控和告警

建议配置日志监控：
- 监控ERROR级别日志数量
- 监控日志文件大小
- 配置关键错误告警

## 故障排查

1. **日志文件不生成**: 检查logs目录权限
2. **日志级别不生效**: 确认环境配置是否正确
3. **性能问题**: 检查是否开启了过多DEBUG日志
