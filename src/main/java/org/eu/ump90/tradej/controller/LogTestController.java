package org.eu.ump90.tradej.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日志测试控制器
 * 演示如何在Spring Boot项目中使用日志
 */
@Slf4j
@RestController
@RequestMapping("/api/log")
public class LogTestController {

    /**
     * 测试不同级别的日志输出
     */
    @GetMapping("/test")
    public String testLog(@RequestParam(defaultValue = "测试消息") String message) {
        // TRACE级别日志 - 最详细的日志信息
        log.trace("TRACE级别日志: {}", message);
        
        // DEBUG级别日志 - 调试信息
        log.debug("DEBUG级别日志: 开始处理请求, 参数: {}", message);
        
        // INFO级别日志 - 一般信息
        log.info("INFO级别日志: 用户请求日志测试接口, 消息内容: {}", message);
        
        // WARN级别日志 - 警告信息
        log.warn("WARN级别日志: 这是一个警告消息示例");
        
        // ERROR级别日志 - 错误信息
        log.error("ERROR级别日志: 这是一个错误消息示例");
        
        return "日志测试完成，请查看控制台和日志文件";
    }

    /**
     * 测试异常日志记录
     */
    @GetMapping("/error")
    public String testErrorLog() {
        try {
            // 故意制造一个异常
            int result = 10 / 0;
        } catch (Exception e) {
            // 记录异常日志，包含异常堆栈信息
            log.error("发生除零异常", e);
            return "异常已记录到日志中";
        }
        return "正常执行";
    }

    /**
     * 测试结构化日志记录
     */
    @GetMapping("/structured")
    public String testStructuredLog(@RequestParam(defaultValue = "user123") String userId,
                                   @RequestParam(defaultValue = "login") String action) {
        // 使用结构化日志记录，便于后续日志分析
        log.info("用户操作日志 - 用户ID: {}, 操作: {}, 时间戳: {}", 
                userId, action, System.currentTimeMillis());
        
        // 记录业务流程日志
        log.info("业务流程开始 - 流程: {}, 用户: {}", action, userId);
        
        // 模拟业务处理
        try {
            Thread.sleep(100); // 模拟处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("线程被中断", e);
        }
        
        log.info("业务流程结束 - 流程: {}, 用户: {}, 状态: 成功", action, userId);
        
        return "结构化日志记录完成";
    }

    /**
     * 测试性能日志记录
     */
    @GetMapping("/performance")
    public String testPerformanceLog() {
        long startTime = System.currentTimeMillis();
        
        log.info("性能监控开始 - 接口: /api/log/performance");
        
        try {
            // 模拟一些处理时间
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("线程被中断", e);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 记录性能日志
        log.info("性能监控结束 - 接口: /api/log/performance, 耗时: {}ms", duration);
        
        // 如果耗时过长，记录警告
        if (duration > 500) {
            log.warn("接口响应时间过长 - 接口: /api/log/performance, 耗时: {}ms", duration);
        }
        
        return String.format("性能测试完成，耗时: %dms", duration);
    }
}
