package org.eu.ump90.tradej.service.impl;

import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eu.ump90.tradej.api.BinanceAPI;
import org.eu.ump90.tradej.bean.domain.Kline;
import org.eu.ump90.tradej.mapper.KlineMapper;
import org.eu.ump90.tradej.service.SyncDataService;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class SyncDataServiceImpl implements SyncDataService {
  @Resource private KlineMapper klineMapper;
  @Resource private BinanceAPI binanceAPI;

  public static List<String> getMonthsBetween(LocalDate startDate, LocalDate endDate) {
    List<String> months = new ArrayList<>();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

    YearMonth start = YearMonth.from(startDate);
    YearMonth end = YearMonth.from(endDate);

    for (YearMonth month = start; !month.isAfter(end); month = month.plusMonths(1)) {
      months.add(month.format(formatter));
    }

    return months;
  }

  @Override
  public void syncData(String symbol, String interval, String startTime, String endTime) {
    try {
      log.info(
          "开始同步数据: symbol={}, interval={}, startTime={}, endTime={}",
          symbol,
          interval,
          startTime,
          endTime);

      // 解析开始和结束时间
      LocalDate start = LocalDate.parse(startTime);
      LocalDate end = LocalDate.parse(endTime);

      // 获取月份列表
      List<String> months = getMonthsBetween(start, end);
      log.info("需要同步的月份: {}", months);

      // 遍历每个月份下载并处理数据
      for (String month : months) {
        String[] parts = month.split("-");
        String year = parts[0];
        String monthStr = parts[1];

        try {
          log.info("开始处理月份: {}", month);
          processMonthData(symbol, interval, year, monthStr);
          log.info("完成处理月份: {}", month);
        } catch (Exception e) {
          log.error("处理月份 {} 时发生错误: {}", month, e.getMessage(), e);
          // 继续处理下一个月份，不中断整个流程
        }
      }

      log.info("数据同步完成");
    } catch (Exception e) {
      log.error("同步数据时发生错误: {}", e.getMessage(), e);
      throw new RuntimeException("数据同步失败", e);
    }
  }

  /** 处理单个月份的数据 */
  private void processMonthData(String symbol, String interval, String year, String month)
      throws IOException {
    // 下载ZIP文件
    InputStream zipStream = binanceAPI.downloadZipFile(symbol, interval, year, month);

    if (zipStream == null) {
      log.warn("无法下载文件: {}-{}-{}-{}", symbol, interval, year, month);
      return;
    }

    // 解压并处理CSV文件
    try (ZipInputStream zis = new ZipInputStream(zipStream)) {
      ZipEntry entry;
      while ((entry = zis.getNextEntry()) != null) {
        if (entry.getName().endsWith(".csv")) {
          log.info("处理CSV文件: {}", entry.getName());
          List<Kline> klineData = parseCsvData(zis, symbol, interval);

          if (!klineData.isEmpty()) {
            // 批量插入数据库
            int insertCount = klineMapper.batchInsert(klineData);
            log.info("成功插入 {} 条数据", insertCount);
          } else {
            log.warn("CSV文件中没有有效数据");
          }
          break; // 只处理第一个CSV文件
        }
        zis.closeEntry();
      }
    }
  }

  /** 解析CSV数据 */
  private List<Kline> parseCsvData(InputStream csvStream, String symbol, String interval)
      throws IOException {
    List<Kline> klineList = new ArrayList<>();

    try (BufferedReader reader = new BufferedReader(new InputStreamReader(csvStream))) {
      String line;
      int lineNumber = 0;

      while ((line = reader.readLine()) != null) {
        lineNumber++;
        try {
          Kline kline = parseCsvLine(line, symbol);
          if (kline != null) {
            kline.setInterval(interval);
            klineList.add(kline);
          }
        } catch (Exception e) {
          log.warn("解析第 {} 行数据时发生错误: {}, 数据: {}", lineNumber, e.getMessage(), line);
        }
      }
    }

    log.info("解析完成，共 {} 条有效数据", klineList.size());
    return klineList;
  }

  /**
   * 解析单行CSV数据 CSV格式: Open time,Open,High,Low,Close,Volume,Close time,Quote asset volume,Number of
   * trades,Taker buy base asset volume,Taker buy quote asset volume,Ignore
   */
  private Kline parseCsvLine(String line, String symbol) {
    if (line == null || line.trim().isEmpty()) {
      return null;
    }

    String[] fields = line.split(",");
    if (fields.length < 12) {
      log.warn("CSV行数据字段不足: {}", line);
      return null;
    }

    try {
      Kline kline = new Kline();
      kline.setSymbol(symbol);
      kline.setOpenTime(Long.parseLong(fields[0]));
      kline.setOpen(new BigDecimal(fields[1]));
      kline.setHigh(new BigDecimal(fields[2]));
      kline.setLow(new BigDecimal(fields[3]));
      kline.setClose(new BigDecimal(fields[4]));
      kline.setVolume(new BigDecimal(fields[5]));
      kline.setCloseTime(Long.parseLong(fields[6]));
      kline.setQuoteAssetVolume(new BigDecimal(fields[7]));
      kline.setNumberOfTrades(Integer.parseInt(fields[8]));
      kline.setTakerBuyBaseAssetVolume(new BigDecimal(fields[9]));
      kline.setTakerBuyQuoteAssetVolume(new BigDecimal(fields[10]));
      kline.setIgnoreField(fields.length > 11 ? fields[11] : "");

      return kline;
    } catch (NumberFormatException e) {
      log.warn("解析数字字段时发生错误: {}, 数据: {}", e.getMessage(), line);
      return null;
    }
  }
}
