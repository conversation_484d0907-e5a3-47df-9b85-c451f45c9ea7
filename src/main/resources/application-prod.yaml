# 生产环境配置
logging:
  level:
    root: WARN
    org.eu.ump: INFO  # 项目包使用INFO级别
    org.springframework.web: WARN  # Spring Web框架日志
    org.mybatis: WARN  # MyBatis日志，生产环境不输出SQL
    com.dtflys.forest: WARN  # Forest HTTP客户端日志
    org.springframework.jdbc: ERROR  # 数据库连接日志只记录错误
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    max-size: 100MB  # 单个日志文件最大大小
    max-history: 30  # 保留30天的日志文件

# MyBatis生产环境配置
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 生产环境使用SLF4J
    map-underscore-to-camel-case: true

# Forest生产环境配置
forest:
  log-enabled: false  # 生产环境关闭详细日志
  log-request: false
  log-response-status: false
  log-response-content: false
