# 开发环境配置
logging:
  level:
    root: INFO
    org.eu.ump: DEBUG  # 项目包使用DEBUG级别，便于开发调试
    org.springframework.web: DEBUG  # Spring Web框架日志
    org.mybatis: DEBUG  # MyBatis SQL日志
    com.dtflys.forest: DEBUG  # Forest HTTP客户端日志
    org.springframework.jdbc: DEBUG  # 数据库连接日志
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr([%thread]){blue} %clr(%-5level){highlight} %clr(%logger{36}){cyan} - %msg%n"

# MyBatis开发环境配置
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 开发环境使用控制台输出SQL
    map-underscore-to-camel-case: true

# Forest开发环境配置
forest:
  log-enabled: true
  log-request: true
  log-response-status: true
  log-response-content: true
