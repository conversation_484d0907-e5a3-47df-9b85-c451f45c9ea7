# 测试环境配置
logging:
  level:
    root: INFO
    org.eu.ump: INFO  # 项目包使用INFO级别
    org.springframework.web: WARN  # Spring Web框架日志
    org.mybatis: INFO  # MyBatis日志
    com.dtflys.forest: INFO  # Forest HTTP客户端日志
    org.springframework.jdbc: WARN  # 数据库连接日志
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# MyBatis测试环境配置
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 测试环境使用SLF4J
    map-underscore-to-camel-case: true

# Forest测试环境配置
forest:
  log-enabled: true
  log-request: false  # 测试环境不记录请求详情
  log-response-status: true
  log-response-content: false  # 测试环境不记录响应内容
